// using Azure;
// using Azure.Data.Tables;
// using Avanade.Geranium.Attane.Business.Entities;

// namespace Avanade.Geranium.Attane.Infrastructure.Data
// {
//     /// <summary>
//     /// Teams チャット設定のTable Storage エンティティ.
//     /// </summary>
//     public class TeamsChatsTableEntity : ITableEntity
//     {
//         /// <summary>
//         /// Gets or sets the partition key (UserId).
//         /// </summary>
//         public string PartitionKey { get; set; } = string.Empty;

//         /// <summary>
//         /// Gets or sets the row key (Id).
//         /// </summary>
//         public string RowKey { get; set; } = string.Empty;

//         /// <summary>
//         /// Gets or sets the timestamp.
//         /// </summary>
//         public DateTimeOffset? Timestamp { get; set; }

//         /// <summary>
//         /// Gets or sets the ETag.
//         /// </summary>
//         public ETag ETag { get; set; }

//         /// <summary>
//         /// Gets or sets the チャットID.
//         /// </summary>
//         public string? ChatId { get; set; }

//         /// <summary>
//         /// Gets or sets the チャネルID.
//         /// </summary>
//         public string? ChannelId { get; set; }

//         /// <summary>
//         /// Gets or sets the チャットタイプ.
//         /// </summary>
//         public string? ChatType { get; set; }

//         /// <summary>
//         /// Gets or sets the 名前.
//         /// </summary>
//         public string? Name { get; set; }

//         /// <summary>
//         /// Gets or sets the 作成日時.
//         /// </summary>
//         public DateTime? CreatedAt { get; set; }

//         /// <summary>
//         /// Gets or sets the 更新日時.
//         /// </summary>
//         public DateTime? UpdatedAt { get; set; }

//         /// <summary>
//         /// Converts from <see cref="TeamsChats"/> to <see cref="TeamsChatsTableEntity"/>.
//         /// </summary>
//         /// <param name="teamsChats">The <see cref="TeamsChats"/>.</param>
//         /// <returns>The <see cref="TeamsChatsTableEntity"/>.</returns>
//         public static TeamsChatsTableEntity FromTeamsChats(TeamsChats teamsChats)
//         {
//             return new TeamsChatsTableEntity
//             {
//                 PartitionKey = teamsChats.UserId ?? string.Empty,
//                 RowKey = teamsChats.Id ?? string.Empty,
//                 ChatId = teamsChats.ChatId,
//                 ChannelId = teamsChats.ChannelId,
//                 ChatType = teamsChats.ChatType,
//                 Name = teamsChats.Name,
//                 CreatedAt = teamsChats.CreatedAt,
//                 UpdatedAt = teamsChats.UpdatedAt
//             };
//         }

//         /// <summary>
//         /// Converts from <see cref="TeamsChatsTableEntity"/> to <see cref="TeamsChats"/>.
//         /// </summary>
//         /// <returns>The <see cref="TeamsChats"/>.</returns>
//         public TeamsChats ToTeamsChats()
//         {
//             return new TeamsChats
//             {
//                 Id = RowKey,
//                 UserId = PartitionKey,
//                 ChatId = ChatId,
//                 ChannelId = ChannelId,
//                 ChatType = ChatType,
//                 Name = Name,
//                 CreatedAt = CreatedAt,
//                 UpdatedAt = UpdatedAt
//             };
//         }
//     }
// }
