/*
 * This file is automatically generated; any changes will be lost.
 */

#nullable enable
#pragma warning disable

using CoreEx.Entities;
using Avanade.Geranium.Attane.Business.Entities;
using Avanade.Geranium.Attane.Business.DataSvc;

namespace Avanade.Geranium.Attane.Business
{
    /// <summary>
    /// Provides the <see cref="TeamsChats"/> business functionality.
    /// </summary>
    public partial class TeamsChatsManager : ITeamsChatsManager
    {
        private readonly ITeamsChatsDataSvc _dataService;

        /// <summary>
        /// Initializes a new instance of the <see cref="TeamsChatsManager"/> class.
        /// </summary>
        /// <param name="dataService">The <see cref="ITeamsChatsDataSvc"/>.</param>
        public TeamsChatsManager(ITeamsChatsDataSvc dataService)
        {
            _dataService = dataService ?? throw new ArgumentNullException(nameof(dataService));
            TeamsChatsManagerCtor();
        }

        partial void TeamsChatsManagerCtor(); // Enables additional functionality to be added to the constructor.

        /// <summary>
        /// Teams チャット設定を作成します.
        /// </summary>
        /// <param name="value">The <see cref="TeamsChats"/>.</param>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <returns>The created <see cref="TeamsChats"/>.</returns>
        public Task<TeamsChats> CreateAsync(TeamsChats value, string userId) => ManagerInvoker.Current.InvokeAsync(this, async _ =>
        {
            return await CreateOnImplementationAsync(value, userId).ConfigureAwait(false);
        }, InvokerArgs.Create);

        /// <summary>
        /// 指定されたユーザーのTeams チャット設定を取得します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <returns>The <see cref="TeamsChatsCollection"/>.</returns>
        public Task<TeamsChatsCollection> GetByUserIdAsync(string userId) => ManagerInvoker.Current.InvokeAsync(this, async _ =>
        {
            return await GetByUserIdOnImplementationAsync(userId).ConfigureAwait(false);
        }, InvokerArgs.Read);

        /// <summary>
        /// Teams チャット設定を削除します.
        /// </summary>
        /// <param name="id">The Teams チャット設定 ID.</param>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        public Task DeleteAsync(string id, string userId) => ManagerInvoker.Current.InvokeAsync(this, async _ =>
        {
            await DeleteOnImplementationAsync(id, userId).ConfigureAwait(false);
        }, InvokerArgs.Delete);
    }
}

#pragma warning restore
#nullable restore
