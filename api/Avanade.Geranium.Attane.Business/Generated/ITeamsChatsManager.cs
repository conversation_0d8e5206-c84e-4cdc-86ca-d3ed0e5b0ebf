/*
 * This file is automatically generated; any changes will be lost.
 */

#nullable enable
#pragma warning disable

using Avanade.Geranium.Attane.Business.Entities;

namespace Avanade.Geranium.Attane.Business
{
    /// <summary>
    /// Provides the <see cref="TeamsChats"/> business functionality.
    /// </summary>
    public partial interface ITeamsChatsManager
    {
        /// <summary>
        /// Teams チャット設定を作成します.
        /// </summary>
        /// <param name="value">The <see cref="TeamsChats"/>.</param>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <returns>The created <see cref="TeamsChats"/>.</returns>
        Task<TeamsChats> CreateAsync(TeamsChats value, string userId);

        /// <summary>
        /// 指定されたユーザーのTeams チャット設定を取得します.
        /// </summary>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <returns>The <see cref="TeamsChatsCollection"/>.</returns>
        Task<TeamsChatsCollection> GetByUserIdAsync(string userId);

        /// <summary>
        /// Teams チャット設定を削除します.
        /// </summary>
        /// <param name="id">The Teams チャット設定 ID.</param>
        /// <param name="userId">The 対象のユーザーID.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        Task DeleteAsync(string id, string userId);
    }
}

#pragma warning restore
#nullable restore
