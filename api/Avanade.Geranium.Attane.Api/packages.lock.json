{"version": 1, "dependencies": {"net8.0": {"Avanade.Teams.Auth": {"type": "Direct", "requested": "[6.0.2, )", "resolved": "6.0.2", "contentHash": "/e05xloX4iHiHBt7OVOAn2OhiI2y3PLCergKLgGyc6+1N156YQPgnoVMcVGE7/BPEGB5UycqkzbvqJxX5T4XBA==", "dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "6.19.0"}}, "Azure.AI.OpenAI": {"type": "Direct", "requested": "[2.1.0, )", "resolved": "2.1.0", "contentHash": "doixr3tcsIcdzbzF9NxKt+6U0NKj6aPeOCPYONPsWjyf3gxVndVJAdjPcVdqeR/3vcRHXRgGnGlv+iXx5jMA4g==", "dependencies": {"Azure.Core": "1.44.1", "OpenAI": "2.1.0"}}, "Azure.Identity": {"type": "Direct", "requested": "[1.13.1, )", "resolved": "1.13.1", "contentHash": "4eeK9XztjTmvA4WN+qAvlUCSxSv45+LqTMeC8XT2giGGZHKthTMU2IuXcHjAOf5VLH3wE3Bo6EwhIcJxVB8RmQ==", "dependencies": {"Azure.Core": "1.44.1", "Microsoft.Identity.Client": "4.66.1", "Microsoft.Identity.Client.Extensions.Msal": "4.66.1", "System.Memory": "4.5.5", "System.Text.Json": "6.0.10", "System.Threading.Tasks.Extensions": "4.5.4"}}, "Azure.Search.Documents": {"type": "Direct", "requested": "[11.7.0-beta.1, )", "resolved": "11.7.0-beta.1", "contentHash": "5S21uq7tleVDl23BLF1c4koyTwG7fCcaby9mIt7n0AhYj/UfaVgF5aweisoDSpj1NrErkxm9+c5sYoLOgKN3LQ==", "dependencies": {"Azure.Core": "1.43.0", "System.Text.Json": "6.0.9", "System.Threading.Channels": "6.0.0"}}, "CoreEx.Azure": {"type": "Direct", "requested": "[2.10.1, )", "resolved": "2.10.1", "contentHash": "6ubBhuYmi6aSNfeUpJ6/+hqWV9uvSAilIefhcxX7HgIgGxOQWvqwxb9UYFrQmP3/dEFLtfVTgxQLTcu4GuY/Tg==", "dependencies": {"Azure.Messaging.ServiceBus": "7.13.1", "Azure.Storage.Blobs": "12.16.0", "CoreEx": "2.10.1", "Microsoft.Azure.WebJobs.Extensions.Http": "3.2.0", "Microsoft.Azure.WebJobs.Extensions.ServiceBus": "5.9.0", "Microsoft.Extensions.Configuration.AzureAppConfiguration": "6.0.0"}}, "Microsoft.ApplicationInsights": {"type": "Direct", "requested": "[2.23.0, )", "resolved": "2.23.0", "contentHash": "nWArUZTdU7iqZLycLKWe0TDms48KKGE6pONH2terYNa8REXiqixrMOkf1sk5DHGMaUTqONU2YkS4SAXBhLStgw==", "dependencies": {"System.Diagnostics.DiagnosticSource": "5.0.0"}}, "Microsoft.ApplicationInsights.AspNetCore": {"type": "Direct", "requested": "[2.23.0, )", "resolved": "2.23.0", "contentHash": "we/RsIn0Mwf/4ZNGXZixJ0lVD3pqjx2yVeKfqJybgYY/Lib8nnf+8YGJp+ULN3kOk39I0pI/7ZnF9LFy6hS3lw==", "dependencies": {"Microsoft.ApplicationInsights": "2.23.0", "Microsoft.ApplicationInsights.DependencyCollector": "2.23.0", "Microsoft.ApplicationInsights.EventCounterCollector": "2.23.0", "Microsoft.ApplicationInsights.PerfCounterCollector": "2.23.0", "Microsoft.ApplicationInsights.WindowsServer": "2.23.0", "Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel": "2.23.0", "Microsoft.AspNetCore.Hosting": "2.1.1", "Microsoft.AspNetCore.Http": "2.1.22", "Microsoft.Extensions.Configuration.Json": "3.1.0", "Microsoft.Extensions.Logging.ApplicationInsights": "2.23.0", "System.Text.Encodings.Web": "4.7.2"}}, "Microsoft.Identity.Web": {"type": "Direct", "requested": "[3.8.1, )", "resolved": "3.8.1", "contentHash": "ecaVdMrEwtkJPqyl486W0ajPLkaJ9rFDFB33o8x7G91BWha0qvrqdlqVBdc7zEWAqTRdLWfx5+gHFXOaAR+KTw==", "dependencies": {"Microsoft.Identity.Web.Certificate": "3.8.1", "Microsoft.Identity.Web.Certificateless": "3.8.1", "Microsoft.Identity.Web.TokenAcquisition": "3.8.1", "Microsoft.Identity.Web.TokenCache": "3.8.1", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "8.7.0", "Microsoft.IdentityModel.Validators": "8.7.0", "System.Formats.Asn1": "8.0.1", "System.IdentityModel.Tokens.Jwt": "8.7.0", "System.Text.Json": "8.0.5"}}, "Microsoft.IdentityModel.JsonWebTokens": {"type": "Direct", "requested": "[8.7.0, )", "resolved": "8.7.0", "contentHash": "uzsSAWhNhbrkWbQKBTE8QhzviU6sr3bJ1Bkv7gERlhswfSKOp7HsxTRLTPBpx/whQ/GRRHEwMg8leRIPbMrOgw==", "dependencies": {"Microsoft.IdentityModel.Tokens": "8.7.0"}}, "Swashbuckle.AspNetCore": {"type": "Direct", "requested": "[8.0.0, )", "resolved": "8.0.0", "contentHash": "K9FzGTxmwfD+7sVf/FTq/TZFHBTXcROgdcg7gLFwKwgvXwaqTtjGVdam27j0kYfgZZyWlOKr+abmtyd2nAd5eA==", "dependencies": {"Microsoft.Extensions.ApiDescription.Server": "6.0.5", "Swashbuckle.AspNetCore.Swagger": "8.0.0", "Swashbuckle.AspNetCore.SwaggerGen": "8.0.0", "Swashbuckle.AspNetCore.SwaggerUI": "8.0.0"}}, "Swashbuckle.AspNetCore.Newtonsoft": {"type": "Direct", "requested": "[8.0.0, )", "resolved": "8.0.0", "contentHash": "j8ZEX5k5zWIzkFqEbo/SX22mJey/sZUiBJWhumLfRUkfzJwG/c0oROpX+15EeiIAqnFWAvVRRejQ6ADYYYOw/g==", "dependencies": {"Microsoft.AspNetCore.Mvc.NewtonsoftJson": "8.0.0", "Swashbuckle.AspNetCore.SwaggerGen": "8.0.0"}}, "Azure.Core": {"type": "Transitive", "resolved": "1.45.0", "contentHash": "a4ZZ8SHrYqDYqan0vUZrCoN2V09sSXWqsy5PwgyRDZWF/ZjYPUNqBcOOV01Mm7Rcd4Psf08GUBPOhGz+7onmPQ==", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "6.0.0", "System.ClientModel": "1.2.1", "System.Memory.Data": "6.0.0", "System.Threading.Tasks.Extensions": "4.5.4"}}, "Azure.Core.Amqp": {"type": "Transitive", "resolved": "1.3.0", "contentHash": "6GG4gyFkAuHtpBVkvj0wE5+lCM+ttsZlIWAipBkI+jlCUlTgrTiNUROBFnb8xuKoymVDw9Tf1W8RoKqgbd71lg==", "dependencies": {"Microsoft.Azure.Amqp": "2.6.1", "System.Memory": "4.5.4", "System.Memory.Data": "1.0.2"}}, "Azure.Data.AppConfiguration": {"type": "Transitive", "resolved": "1.2.0", "contentHash": "KA1dAM9TuDsq0CRFd+3cJTYUAzA2z9N8t9/xKdDbP9URuReq/NDFcKYr7GW2W9xzVGDtCHlD5j5am/+zLLBdSg==", "dependencies": {"Azure.Core": "1.20.0", "Microsoft.Bcl.AsyncInterfaces": "1.0.0", "System.Text.Json": "4.6.0"}}, "Azure.Data.Tables": {"type": "Transitive", "resolved": "12.10.0", "contentHash": "fW6873rvhpAYUGXzu7JmbfSkYfppvfkKkiZqifSyOVJ0SsAWBMqHWL9VbUPsApP3DWTwakpU35nfpKTPqvFVww==", "dependencies": {"Azure.Core": "1.44.1"}}, "Azure.Messaging.EventGrid": {"type": "Transitive", "resolved": "4.7.0", "contentHash": "Wm5+RY6hNoIPVLPwmr3T1ijVm5GdLVZBij93c4Brwe9iB3X8nlUYNjlnQVVJqK4QLs85nGwqBGUpB4BfYdGXVQ==", "dependencies": {"Azure.Core": "1.20.0", "System.Memory.Data": "1.0.2", "System.Text.Json": "4.6.0"}}, "Azure.Messaging.ServiceBus": {"type": "Transitive", "resolved": "7.13.1", "contentHash": "kkTAoppWEbu+xVncpRyNhYLs6mEMv/A0YOpOm6Fh3XvDDd/TBd2c8vha8D+kPzn8mI+A7EOlI/giIDgCSCo7Ww==", "dependencies": {"Azure.Core": "1.30.0", "Azure.Core.Amqp": "1.3.0", "Microsoft.Azure.Amqp": "2.6.1", "Microsoft.Bcl.AsyncInterfaces": "1.1.1", "System.Memory.Data": "1.0.2"}}, "Azure.Security.KeyVault.Certificates": {"type": "Transitive", "resolved": "4.6.0", "contentHash": "e2ATU/n2ZDL/S8A8EdrcfKEvKc2BojCrrSpmM+JKnrSTQS32x/W0Ldu8utk+epLKwXvSJRSWtlgdo7X8hG1mCg==", "dependencies": {"Azure.Core": "1.37.0", "System.Memory": "4.5.4", "System.Text.Json": "4.7.2", "System.Threading.Tasks.Extensions": "4.5.4"}}, "Azure.Security.KeyVault.Secrets": {"type": "Transitive", "resolved": "4.6.0", "contentHash": "vwPceoznuT6glvirZcXlaCQrh1uzTSxpZUi2hRFNumHiS3hVyqIXI5fgWiLtlBzwqPJMTr0flUoSvGKjXXQlfg==", "dependencies": {"Azure.Core": "1.37.0", "System.Memory": "4.5.4", "System.Text.Json": "4.7.2", "System.Threading.Tasks.Extensions": "4.5.4"}}, "Azure.Storage.Blobs": {"type": "Transitive", "resolved": "12.16.0", "contentHash": "1ibzh49byOzB2ds6k9bsPqXvxxzdc2U9+MmooDr/lYJHgaWEnPZYX/i04vH0oN0jBGN1diW4N27xER8npvOzCw==", "dependencies": {"Azure.Storage.Common": "12.15.0", "System.Text.Json": "4.7.2"}}, "Azure.Storage.Common": {"type": "Transitive", "resolved": "12.23.0", "contentHash": "X/pe1LS3lC6s6MSL7A6FzRfnB6P72rNBt5oSuyan6Q4Jxr+KiN9Ufwqo32YLHOVfPcB8ESZZ4rBDketn+J37Rw==", "dependencies": {"Azure.Core": "1.44.1", "System.IO.Hashing": "6.0.0"}}, "Azure.Storage.Queues": {"type": "Transitive", "resolved": "12.22.0", "contentHash": "HPQgOlfH+rJ4CL4V8ePFnsT/KKnvLU35ytxC3fsTTqOazhQ0593C0aPVu258DRN8bQCbx4OpNpjtiO9czDy3VQ==", "dependencies": {"Azure.Storage.Common": "12.23.0", "System.Memory.Data": "6.0.1"}}, "CloudNative.CloudEvents": {"type": "Transitive", "resolved": "2.6.0", "contentHash": "VIcb3tQZcluVEqO6yhnFIpMaPYq5Ime1JbqxLeGPZ+CqeVDOZTBGAtOGQPgfadv2tZFU7+jZqcX0Oplrs7oR7w==", "dependencies": {"System.Memory": "4.5.5"}}, "CloudNative.CloudEvents.SystemTextJson": {"type": "Transitive", "resolved": "2.6.0", "contentHash": "pDrmaQqFYhVOHsg5LpAsLbiL5jd67RWilN0854gM5RbUkAOVWSZTT3m2F8HnVsjq22YfZrwuOkDaLBYFOjiFZQ==", "dependencies": {"CloudNative.CloudEvents": "2.6.0", "System.Text.Json": "5.0.2"}}, "CoreEx": {"type": "Transitive", "resolved": "2.10.1", "contentHash": "THAh81GNe7HKRTwLLFmcTRkBpfPk5WeLwgFnRjH9WChtxJkvfchYUKSLHTVscek+A3j7CbLMu7v4Q+m0NOEzuw==", "dependencies": {"CloudNative.CloudEvents.SystemTextJson": "2.6.0", "Microsoft.AspNetCore.Mvc.Core": "2.2.5", "Microsoft.Extensions.Caching.Memory": "6.0.1", "Microsoft.Extensions.Configuration.Binder": "6.0.0", "Microsoft.Extensions.Diagnostics.HealthChecks": "6.0.16", "Microsoft.Extensions.Hosting.Abstractions": "6.0.0", "Microsoft.Extensions.Http": "6.0.0", "Microsoft.Extensions.Logging": "6.0.0", "Polly": "7.2.3", "Polly.Extensions.Http": "3.0.0", "Swashbuckle.AspNetCore": "6.5.0", "System.Collections.Immutable": "6.0.0", "System.ComponentModel.Annotations": "5.0.0", "System.Memory.Data": "6.0.0", "System.Text.Json": "6.0.7", "YamlDotNet": "13.1.0"}}, "CoreEx.Validation": {"type": "Transitive", "resolved": "2.10.1", "contentHash": "PQpx41jaIvyFHNmP1N/PF4UEDMzovwPIBMF7Hdy3xKGu6w91uX7gJ4qAQuLYtPyYta71mUlYZRdWXnsSNBxJLQ==", "dependencies": {"CoreEx": "2.10.1"}}, "Microsoft.ApplicationInsights.DependencyCollector": {"type": "Transitive", "resolved": "2.23.0", "contentHash": "9YRdl9SNbTxd4AafJckyoJLr5gJdnvqFivjo+PY0lQTPEncPB+z3ZABG4iDfxN9HI1aLqyRINr1/7de9Wg8ZuQ==", "dependencies": {"Microsoft.ApplicationInsights": "2.23.0", "System.Diagnostics.DiagnosticSource": "5.0.0"}}, "Microsoft.ApplicationInsights.EventCounterCollector": {"type": "Transitive", "resolved": "2.23.0", "contentHash": "gGt0JPw2dcSeIAIefyORJBdeMz8KgAFIktu8HV/NwkiGmLyw+YtifLm6B5gvGxO15AeMsGPbmvWEIvLfq88XPw==", "dependencies": {"Microsoft.ApplicationInsights": "2.23.0"}}, "Microsoft.ApplicationInsights.PerfCounterCollector": {"type": "Transitive", "resolved": "2.23.0", "contentHash": "q9ApjZfBS9O8m3aQM2oVjsGBmlE8BCFywT7UR+8aqdNuz7HpoIxw4jHy0XOBergiFX/olrJF4OyPkGxc3H5JHg==", "dependencies": {"Microsoft.ApplicationInsights": "2.23.0", "Microsoft.Extensions.Caching.Memory": "1.0.0", "System.Diagnostics.PerformanceCounter": "6.0.0"}}, "Microsoft.ApplicationInsights.WindowsServer": {"type": "Transitive", "resolved": "2.23.0", "contentHash": "2B8CGfnB/tribkQAqRBhMvJYJK5TkEPMG/BB0QrlxdwVGEufayNLMveXjkQCqld9arXd6wKR1ve2XmkA0+xXKQ==", "dependencies": {"Microsoft.ApplicationInsights": "2.23.0", "Microsoft.ApplicationInsights.DependencyCollector": "2.23.0", "Microsoft.ApplicationInsights.PerfCounterCollector": "2.23.0", "Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel": "2.23.0", "System.Diagnostics.DiagnosticSource": "5.0.0"}}, "Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel": {"type": "Transitive", "resolved": "2.23.0", "contentHash": "798Dudr4tkujslk1w+XcXOcCErmVsk+nhp+QCHLa3lcgi25vkAxBmzPUeQlRJVCNL/1f4x/YF+vQZ8RSuTXWCw==", "dependencies": {"Microsoft.ApplicationInsights": "2.23.0", "System.IO.FileSystem.AccessControl": "4.7.0"}}, "Microsoft.AspNet.WebApi.Client": {"type": "Transitive", "resolved": "5.2.8", "contentHash": "dkGLm30CxLieMlUO+oQpJw77rDs0IIx/w3lIHsp+8X94HXCsUfLYuFmlZPsQDItC0O2l1ZlWeKLkZX7ZiNRekw==", "dependencies": {"Newtonsoft.Json": "10.0.1", "Newtonsoft.Json.Bson": "1.0.1"}}, "Microsoft.AspNetCore.Authentication.Abstractions": {"type": "Transitive", "resolved": "2.2.0", "contentHash": "VloMLDJMf3n/9ic5lCBOa42IBYJgyB1JhzLsL68Zqg+2bEPWfGBj/xCJy/LrKTArN0coOcZp3wyVTZlx0y9pHQ==", "dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "2.2.0"}}, "Microsoft.AspNetCore.Authentication.Core": {"type": "Transitive", "resolved": "2.2.0", "contentHash": "XlVJzJ5wPOYW+Y0J6Q/LVTEyfS4ssLXmt60T0SPP+D8abVhBTl+cgw2gDHlyKYIkcJg7btMVh383NDkMVqD/fg==", "dependencies": {"Microsoft.AspNetCore.Authentication.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http": "2.2.0", "Microsoft.AspNetCore.Http.Extensions": "2.2.0"}}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "rwxaZYHips5M9vqxRkGfJthTx+Ws4O4yCuefn17J371jL3ouC5Ker43h2hXb5yd9BMnImE9rznT75KJHm6bMgg==", "dependencies": {"Microsoft.IdentityModel.Protocols.OpenIdConnect": "7.0.3"}}, "Microsoft.AspNetCore.Authentication.OpenIdConnect": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "vBAcj4GpCpvJXIXFnYXDVF0yQtsRAaGib+DiMc79KZNyb/TKhxpoHQwOP7v3aMAoIqUC0HUbf1RQJUoOygakbQ==", "dependencies": {"Microsoft.IdentityModel.Protocols.OpenIdConnect": "7.0.3"}}, "Microsoft.AspNetCore.Authorization": {"type": "Transitive", "resolved": "2.2.0", "contentHash": "/L0W8H3jMYWyaeA9gBJqS/tSWBegP9aaTM0mjRhxTttBY9z4RVDRYJ2CwPAmAXIuPr3r1sOw+CS8jFVRGHRezQ==", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "2.2.0"}}, "Microsoft.AspNetCore.Authorization.Policy": {"type": "Transitive", "resolved": "2.2.0", "contentHash": "aJCo6niDRKuNg2uS2WMEmhJTooQUGARhV2ENQ2tO5443zVHUo19MSgrgGo9FIrfD+4yKPF8Q+FF33WkWfPbyKw==", "dependencies": {"Microsoft.AspNetCore.Authentication.Abstractions": "2.2.0", "Microsoft.AspNetCore.Authorization": "2.2.0"}}, "Microsoft.AspNetCore.Cryptography.Internal": {"type": "Transitive", "resolved": "8.0.1", "contentHash": "ryzsiEKr1qJ8f/CARxK8/zTX41aGUpoYOrZuKpsWiK6LwnuynxSFrzBDF04bT7xHF/i0EOeqkIRvfIohI/EsTg=="}, "Microsoft.AspNetCore.DataProtection": {"type": "Transitive", "resolved": "8.0.1", "contentHash": "DX2Quy+WRLXPGeWNGtOKyPT2vusNfFI15S9M2DIpaeBqZmXpheoQdlvsPzyn7K8uorcnfUW5ML/vSo26FOQ9BA==", "dependencies": {"Microsoft.AspNetCore.Cryptography.Internal": "8.0.1", "Microsoft.AspNetCore.DataProtection.Abstractions": "8.0.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.1", "System.Security.Cryptography.Xml": "8.0.0"}}, "Microsoft.AspNetCore.DataProtection.Abstractions": {"type": "Transitive", "resolved": "8.0.1", "contentHash": "4n79+eJnSXaqZIx5c6A+Dtl2bIYwcrAujKDfnDJnTkJa0n5NH4UBCCDNEyONW11UeBYzZb1G4DTE7YWOFbw+9Q=="}, "Microsoft.AspNetCore.Hosting": {"type": "Transitive", "resolved": "2.1.1", "contentHash": "MqYc0DUxrhAPnb5b4HFspxsoJT+gJlLsliSxIgovf4BsbmpaXQId0/pDiVzLuEbmks2w1/lRfY8w0lQOuK1jQQ==", "dependencies": {"Microsoft.AspNetCore.Hosting.Abstractions": "2.1.1", "Microsoft.AspNetCore.Http": "2.1.1", "Microsoft.AspNetCore.Http.Extensions": "2.1.1", "Microsoft.Extensions.Configuration": "2.1.1", "Microsoft.Extensions.Configuration.EnvironmentVariables": "2.1.1", "Microsoft.Extensions.Configuration.FileExtensions": "2.1.1", "Microsoft.Extensions.DependencyInjection": "2.1.1", "Microsoft.Extensions.FileProviders.Physical": "2.1.1", "Microsoft.Extensions.Hosting.Abstractions": "2.1.1", "Microsoft.Extensions.Logging": "2.1.1", "Microsoft.Extensions.Options": "2.1.1", "System.Diagnostics.DiagnosticSource": "4.5.0", "System.Reflection.Metadata": "1.6.0"}}, "Microsoft.AspNetCore.Hosting.Abstractions": {"type": "Transitive", "resolved": "2.2.0", "contentHash": "ubycklv+ZY7Kutdwuy1W4upWcZ6VFR8WUXU7l7B2+mvbDBBPAcfpi+E+Y5GFe+Q157YfA3C49D2GCjAZc7Mobw==", "dependencies": {"Microsoft.AspNetCore.Hosting.Server.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.Hosting.Abstractions": "2.2.0"}}, "Microsoft.AspNetCore.Hosting.Server.Abstractions": {"type": "Transitive", "resolved": "2.2.0", "contentHash": "1PMijw8RMtuQF60SsD/JlKtVfvh4NORAhF4wjysdABhlhTrYmtgssqyncR0Stq5vqtjplZcj6kbT4LRTglt9IQ==", "dependencies": {"Microsoft.AspNetCore.Http.Features": "2.2.0", "Microsoft.Extensions.Configuration.Abstractions": "2.2.0"}}, "Microsoft.AspNetCore.Http": {"type": "Transitive", "resolved": "2.2.2", "contentHash": "BAibpoItxI5puk7YJbIGj95arZueM8B8M5xT1fXBn3hb3L2G3ucrZcYXv1gXdaroLbntUs8qeV8iuBrpjQsrKw==", "dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.AspNetCore.WebUtilities": "2.2.0", "Microsoft.Extensions.ObjectPool": "2.2.0", "Microsoft.Extensions.Options": "2.2.0", "Microsoft.Net.Http.Headers": "2.2.0"}}, "Microsoft.AspNetCore.Http.Abstractions": {"type": "Transitive", "resolved": "2.2.0", "contentHash": "Nxs7Z1q3f1STfLYKJSVXCs1iBl+Ya6E8o4Oy1bCxJ/rNI44E/0f6tbsrVqAWfB7jlnJfyaAtIalBVxPKUPQb4Q==", "dependencies": {"Microsoft.AspNetCore.Http.Features": "2.2.0", "System.Text.Encodings.Web": "4.5.0"}}, "Microsoft.AspNetCore.Http.Extensions": {"type": "Transitive", "resolved": "2.2.0", "contentHash": "2DgZ9rWrJtuR7RYiew01nGRzuQBDaGHGmK56Rk54vsLLsCdzuFUPqbDTJCS1qJQWTbmbIQ9wGIOjpxA1t0l7/w==", "dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.FileProviders.Abstractions": "2.2.0", "Microsoft.Net.Http.Headers": "2.2.0", "System.Buffers": "4.5.0"}}, "Microsoft.AspNetCore.Http.Features": {"type": "Transitive", "resolved": "2.2.0", "contentHash": "ziFz5zH8f33En4dX81LW84I6XrYXKf9jg6aM39cM+LffN9KJahViKZ61dGMSO2gd3e+qe5yBRwsesvyqlZaSMg==", "dependencies": {"Microsoft.Extensions.Primitives": "2.2.0"}}, "Microsoft.AspNetCore.JsonPatch": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "klQdb/9+j0u8MDjoqHEgDCPz8GRhfsbRVvZIM3glFqjs8uY7S1hS9RvKZuz8o4dS9NsEpFp4Jccd8CQuIYHK0g==", "dependencies": {"Microsoft.CSharp": "4.7.0", "Newtonsoft.Json": "13.0.3"}}, "Microsoft.AspNetCore.Mvc.Abstractions": {"type": "Transitive", "resolved": "2.2.0", "contentHash": "ET6uZpfVbGR1NjCuLaLy197cQ3qZUjzl7EG5SL4GfJH/c9KRE89MMBrQegqWsh0w1iRUB/zQaK0anAjxa/pz4g==", "dependencies": {"Microsoft.AspNetCore.Routing.Abstractions": "2.2.0", "Microsoft.Net.Http.Headers": "2.2.0"}}, "Microsoft.AspNetCore.Mvc.Core": {"type": "Transitive", "resolved": "2.2.5", "contentHash": "/8sr8ixIUD57UFwUntha9bOwex7/AkZfdk1f9oNJG1Ek7p/uuKVa7fuHmYZpQOf35Oxrt+2Ku4WPwMSbNxOuWg==", "dependencies": {"Microsoft.AspNetCore.Authentication.Core": "2.2.0", "Microsoft.AspNetCore.Authorization.Policy": "2.2.0", "Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http": "2.2.0", "Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.AspNetCore.Mvc.Abstractions": "2.2.0", "Microsoft.AspNetCore.ResponseCaching.Abstractions": "2.2.0", "Microsoft.AspNetCore.Routing": "2.2.0", "Microsoft.AspNetCore.Routing.Abstractions": "2.2.0", "Microsoft.Extensions.DependencyInjection": "2.2.0", "Microsoft.Extensions.DependencyModel": "2.1.0", "Microsoft.Extensions.FileProviders.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0", "System.Diagnostics.DiagnosticSource": "4.5.0", "System.Threading.Tasks.Extensions": "4.5.1"}}, "Microsoft.AspNetCore.Mvc.Formatters.Json": {"type": "Transitive", "resolved": "2.2.0", "contentHash": "ScWwXrkAvw6PekWUFkIr5qa9NKn4uZGRvxtt3DvtUrBYW5Iu2y4SS/vx79JN0XDHNYgAJ81nVs+4M7UE1Y/O+g==", "dependencies": {"Microsoft.AspNetCore.JsonPatch": "2.2.0", "Microsoft.AspNetCore.Mvc.Core": "2.2.0"}}, "Microsoft.AspNetCore.Mvc.NewtonsoftJson": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "/e5+eBvY759xiZJO+y1lHi4VzXqbDzTJSyCtKpaj3Ko2JAFQjiCOJ0ZHk2i4g4HpoSdXmzEXQsjr6dUX9U0/JA==", "dependencies": {"Microsoft.AspNetCore.JsonPatch": "8.0.0", "Newtonsoft.Json": "13.0.3", "Newtonsoft.Json.Bson": "1.0.2"}}, "Microsoft.AspNetCore.Mvc.WebApiCompatShim": {"type": "Transitive", "resolved": "2.2.0", "contentHash": "YKovpp46Fgah0N8H4RGb+7x9vdjj50mS3NON910pYJFQmn20Cd1mYVkTunjy/DrZpvwmJ8o5Es0VnONSYVXEAQ==", "dependencies": {"Microsoft.AspNet.WebApi.Client": "5.2.6", "Microsoft.AspNetCore.Mvc.Core": "2.2.0", "Microsoft.AspNetCore.Mvc.Formatters.Json": "2.2.0", "Microsoft.AspNetCore.WebUtilities": "2.2.0"}}, "Microsoft.AspNetCore.ResponseCaching.Abstractions": {"type": "Transitive", "resolved": "2.2.0", "contentHash": "CIHWEKrHzZfFp7t57UXsueiSA/raku56TgRYauV/W1+KAQq6vevz60zjEKaazt3BI76zwMz3B4jGWnCwd8kwQw==", "dependencies": {"Microsoft.Extensions.Primitives": "2.2.0"}}, "Microsoft.AspNetCore.Routing": {"type": "Transitive", "resolved": "2.2.2", "contentHash": "HcmJmmGYewdNZ6Vcrr5RkQbc/YWU4F79P3uPPBi6fCFOgUewXNM1P4kbPuoem7tN4f7x8mq7gTsm5QGohQ5g/w==", "dependencies": {"Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.AspNetCore.Routing.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0", "Microsoft.Extensions.ObjectPool": "2.2.0", "Microsoft.Extensions.Options": "2.2.0"}}, "Microsoft.AspNetCore.Routing.Abstractions": {"type": "Transitive", "resolved": "2.2.0", "contentHash": "lRRaPN7jDlUCVCp9i0W+PB0trFaKB0bgMJD7hEJS9Uo4R9MXaMC8X2tJhPLmeVE3SGDdYI4QNKdVmhNvMJGgPQ==", "dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0"}}, "Microsoft.AspNetCore.WebUtilities": {"type": "Transitive", "resolved": "2.2.0", "contentHash": "9ErxAAKaDzxXASB/b5uLEkLgUWv1QbeVxyJYEHQwMaxXOeFFVkQxiq8RyfVcifLU7NR0QY0p3acqx4ZpYfhHDg==", "dependencies": {"Microsoft.Net.Http.Headers": "2.2.0", "System.Text.Encodings.Web": "4.5.0"}}, "Microsoft.Azure.Amqp": {"type": "Transitive", "resolved": "2.6.1", "contentHash": "yWnawf7MD/PUNzvLcVkWN0s4kQT3+CUy4sP19CmBnyFX5Uy/CCRC0M6Amkl1p8bn9yht1oSLu+m8d6nc9DLJoA=="}, "Microsoft.Azure.WebJobs": {"type": "Transitive", "resolved": "3.0.36", "contentHash": "S5wppmL8sUfanGmo/zDzpoWrcM+IOvGNFjpkD5XJFCFzrBqktZTEX6MpP/vF3RN6VZm2sFKegZYyce+RNhgE4Q==", "dependencies": {"Microsoft.Azure.WebJobs.Core": "3.0.36", "Microsoft.Extensions.Configuration": "2.1.1", "Microsoft.Extensions.Configuration.Abstractions": "2.1.1", "Microsoft.Extensions.Configuration.EnvironmentVariables": "2.1.0", "Microsoft.Extensions.Configuration.Json": "2.1.0", "Microsoft.Extensions.Hosting": "2.1.0", "Microsoft.Extensions.Logging": "2.1.1", "Microsoft.Extensions.Logging.Abstractions": "2.1.1", "Microsoft.Extensions.Logging.Configuration": "2.1.0", "Newtonsoft.Json": "13.0.1", "System.Memory.Data": "1.0.2", "System.Threading.Tasks.Dataflow": "4.8.0"}}, "Microsoft.Azure.WebJobs.Core": {"type": "Transitive", "resolved": "3.0.36", "contentHash": "4ht/u677qxDL5rviXafYI5K9qtxee8peXQd8JDqG5KBRr5VXct2pgpEs5SnNu+gDO8dAVp+83N1IRiGG2Fl7Nw==", "dependencies": {"System.ComponentModel.Annotations": "4.4.0", "System.Diagnostics.TraceSource": "4.3.0", "System.Memory.Data": "1.0.1"}}, "Microsoft.Azure.WebJobs.Extensions.Http": {"type": "Transitive", "resolved": "3.2.0", "contentHash": "IXLuo5fOliOYKUZjWO5kQ/j3XblM9TNnk1agjzNYkubpDXq6M436GihaVzwTeQlX279P3G1KquS6I+b7pXaFuQ==", "dependencies": {"Microsoft.AspNet.WebApi.Client": "5.2.8", "Microsoft.AspNetCore.Http": "2.2.2", "Microsoft.AspNetCore.Mvc.Formatters.Json": "2.2.0", "Microsoft.AspNetCore.Mvc.WebApiCompatShim": "2.2.0", "Microsoft.AspNetCore.Routing": "2.2.2", "Microsoft.Azure.WebJobs": "3.0.32"}}, "Microsoft.Azure.WebJobs.Extensions.ServiceBus": {"type": "Transitive", "resolved": "5.9.0", "contentHash": "O8N6isudDAHYnsB5j41lfNGXx744iBP94gKbfytusD6H00hKNntFUB/GteuInCvdSoOHyNID72KwBioX+NpZOg==", "dependencies": {"Azure.Messaging.ServiceBus": "7.11.1", "Microsoft.Azure.WebJobs": "3.0.36", "Microsoft.Extensions.Azure": "1.6.0"}}, "Microsoft.Bcl.AsyncInterfaces": {"type": "Transitive", "resolved": "6.0.0", "contentHash": "UcSjPsst+DfAdJGVDsu346FX0ci0ah+lw3WRtn18NUwEqRt70HaOQ7lI72vy3+1LxtqI3T5GWwV39rQSrCzAeg=="}, "Microsoft.Bcl.Memory": {"type": "Transitive", "resolved": "9.0.0", "contentHash": "bTUtGfpGyJnohQzjdXbtc7MqNzkv7CWUSRz54+ucNm0i32rZiIU0VdVPHDBShOl1qhVKRjW8mnEBz3d2vH93tQ=="}, "Microsoft.CSharp": {"type": "Transitive", "resolved": "4.7.0", "contentHash": "pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA=="}, "Microsoft.DotNet.PlatformAbstractions": {"type": "Transitive", "resolved": "2.1.0", "contentHash": "9KPDwvb/hLEVXYruVHVZ8BkebC8j17DmPb56LnqRF74HqSPLjCkrlFUjOtFpQPA2DeADBRTI/e69aCfRBfrhxw==", "dependencies": {"System.AppContext": "4.1.0", "System.Collections": "4.0.11", "System.IO": "4.1.0", "System.IO.FileSystem": "4.0.1", "System.Reflection.TypeExtensions": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.InteropServices": "4.1.0", "System.Runtime.InteropServices.RuntimeInformation": "4.0.0"}}, "Microsoft.Extensions.ApiDescription.Server": {"type": "Transitive", "resolved": "6.0.5", "contentHash": "Ckb5EDBUNJdFWyajfXzUIMRkhf52fHZOQuuZg/oiu8y7zDCVwD0iHhew6MnThjHmevanpxL3f5ci2TtHQEN6bw=="}, "Microsoft.Extensions.Azure": {"type": "Transitive", "resolved": "1.6.0", "contentHash": "Lv1sN8CJi5KuR2WDxLvonJMocTPIsGGFZfk0JZUBK3bWP2TsKwHAKHLIJjID2bAkelFfyalq0nPSB9716db2sA==", "dependencies": {"Azure.Core": "1.25.0", "Azure.Identity": "1.7.0", "Microsoft.Extensions.Configuration.Abstractions": "2.1.0", "Microsoft.Extensions.Configuration.Binder": "2.1.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "2.1.0", "Microsoft.Extensions.Logging.Abstractions": "2.1.0", "Microsoft.Extensions.Options": "2.1.0"}}, "Microsoft.Extensions.Caching.Abstractions": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "3KuSxeHoNYdxVYfg2IRZCThcrlJ1XJqIXkAWikCsbm5C/bCjv7G0WoKDyuR98Q+T607QT2Zl5GsbGRkENcV2yQ==", "dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Caching.Memory": {"type": "Transitive", "resolved": "8.0.1", "contentHash": "HFDnhYLccngrzyGgHkjEDU5FMLn4MpOsr5ElgsBMC4yx6lJh4jeWO7fHS8+TXPq+dgxCmUa/Trl8svObmwW4QA==", "dependencies": {"Microsoft.Extensions.Caching.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Logging.Abstractions": "8.0.2", "Microsoft.Extensions.Options": "8.0.2", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Configuration": {"type": "Transitive", "resolved": "3.1.18", "contentHash": "G2w2QolMv76ZswL6XKjy8PbAu12uYbtIkGwkkZEhy7irWfqQWFT8PKQ68cEpotg6e2rc15E5/LQDgvCpXeWFtA==", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "3.1.18"}}, "Microsoft.Extensions.Configuration.Abstractions": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "3lE/iLSutpgX1CC0NOW70FJoGARRHbyKmG7dc0klnUZ9Dd9hS6N/POPWhKhMLCEuNN5nXEY5agmlFtH562vqhQ==", "dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Configuration.AzureAppConfiguration": {"type": "Transitive", "resolved": "6.0.0", "contentHash": "oRCr8qqBp/37CXttfgbEWnPznHy0/ZCkgNVFFQ5cnXGvG8TBWicnAK032opyrbGIH3n2jYSg/PvIw7aLoOTy3Q==", "dependencies": {"Azure.Data.AppConfiguration": "1.2.0", "Azure.Messaging.EventGrid": "4.7.0", "Azure.Security.KeyVault.Secrets": "4.3.0", "Microsoft.Extensions.Configuration": "3.1.18", "Microsoft.Extensions.DependencyInjection.Abstractions": "3.1.18", "Microsoft.Extensions.Logging": "3.1.18", "System.Text.Json": "4.7.2"}}, "Microsoft.Extensions.Configuration.Binder": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "mBMoXLsr5s1y2zOHWmKsE9veDcx8h1x/c3rz4baEdQKTeDcmQAPNbB54Pi/lhFO3K431eEq6PFbMgLaa6PHFfA==", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}}, "Microsoft.Extensions.Configuration.EnvironmentVariables": {"type": "Transitive", "resolved": "2.1.1", "contentHash": "6xMxFIfKL+7J/jwlk8zV8I61sF3+DRG19iKQxnSfYQU+iMMjGbcWNCHFF/3MHf3o4sTZPZ8D6Io+GwKFc3TIZA==", "dependencies": {"Microsoft.Extensions.Configuration": "2.1.1"}}, "Microsoft.Extensions.Configuration.FileExtensions": {"type": "Transitive", "resolved": "3.1.0", "contentHash": "OjRJIkVxUFiVkr9a39AqVThft9QHoef4But5pDCydJOXJ4D/SkmzuW1tm6J2IXynxj6qfeAz9QTnzQAvOcGvzg==", "dependencies": {"Microsoft.Extensions.Configuration": "3.1.0", "Microsoft.Extensions.FileProviders.Physical": "3.1.0"}}, "Microsoft.Extensions.Configuration.Json": {"type": "Transitive", "resolved": "3.1.0", "contentHash": "gBpBE1GoaCf1PKYC7u0Bd4mVZ/eR2bnOvn7u8GBXEy3JGar6sC3UVpVfTB9w+biLPtzcukZynBG9uchSBbLTNQ==", "dependencies": {"Microsoft.Extensions.Configuration": "3.1.0", "Microsoft.Extensions.Configuration.FileExtensions": "3.1.0"}}, "Microsoft.Extensions.DependencyInjection": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "V8S3bsm50ig6JSyrbcJJ8bW2b9QLGouz+G1miK3UTaOWmMtFwNNNzUf4AleyDWUmTrWMLNnFSLEQtxmxgNQnNQ==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"type": "Transitive", "resolved": "8.0.2", "contentHash": "3iE7UF7MQkCv1cxzCahz+Y/guQbTqieyxyaWKhrRO91itI9cOKO76OHeQDahqG4MmW5umr3CcCvGmK92lWNlbg=="}, "Microsoft.Extensions.DependencyModel": {"type": "Transitive", "resolved": "2.1.0", "contentHash": "nS2XKqi+1A1umnYNLX2Fbm/XnzCxs5i+zXVJ3VC6r9t2z0NZr9FLnJN4VQpKigdcWH/iFTbMuX6M6WQJcTjVIg==", "dependencies": {"Microsoft.DotNet.PlatformAbstractions": "2.1.0", "Newtonsoft.Json": "9.0.1", "System.Diagnostics.Debug": "4.0.11", "System.Dynamic.Runtime": "4.0.11", "System.Linq": "4.1.0"}}, "Microsoft.Extensions.Diagnostics.Abstractions": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "JHYCQG7HmugNYUhOl368g+NMxYE/N/AiclCYRNlgCY9eVyiBkOHMwK4x60RYMxv9EL3+rmj1mqHvdCiPpC+D4Q==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "System.Diagnostics.DiagnosticSource": "8.0.0"}}, "Microsoft.Extensions.Diagnostics.HealthChecks": {"type": "Transitive", "resolved": "6.0.16", "contentHash": "aj1b5amhKQReVWEDsoOW6b1c7/1C+pJHHsI9EEb671uvGsXpiWNEz++1rtw8G8xoeriJmsJEVKaWKnwpG7xPVA==", "dependencies": {"Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions": "6.0.16", "Microsoft.Extensions.Hosting.Abstractions": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.3", "Microsoft.Extensions.Options": "6.0.0"}}, "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions": {"type": "Transitive", "resolved": "9.0.3", "contentHash": "3sNYjO9jUQQVdF5dPoVc5rCV3YKIoUDBZPr9/SuxyxIGH15Wv3/8MvJxeGByufwcM5nhziY2qpbDN50MCMvH0w=="}, "Microsoft.Extensions.FileProviders.Abstractions": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "ZbaMlhJlpisjuWbvXr4LdAst/1XxH3vZ6A0BsgTphZ2L4PGuxRLz7Jr/S7mkAAnOn78Vu0fKhEgNF5JO3zfjqQ==", "dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.FileProviders.Physical": {"type": "Transitive", "resolved": "3.1.0", "contentHash": "KsvgrYp2fhNXoD9gqSu8jPK9Sbvaa7SqNtsLqHugJkCwFmgRvdz76z6Jz2tlFlC7wyMTZxwwtRF8WAorRQWTEA==", "dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "3.1.0", "Microsoft.Extensions.FileSystemGlobbing": "3.1.0"}}, "Microsoft.Extensions.FileSystemGlobbing": {"type": "Transitive", "resolved": "3.1.0", "contentHash": "tK5HZOmVv0kUYkonMjuSsxR0CBk+Rd/69QU3eOMv9FvODGZ2d0SR+7R+n8XIgBcCCoCHJBSsI4GPRaoN3Le4rA=="}, "Microsoft.Extensions.Hosting": {"type": "Transitive", "resolved": "2.1.0", "contentHash": "nqOrLtBqpwRT006vdQ2Vp87uiuYztiZcZAndFqH91ZH4SQgr8wImCVQwzUgTxx1DSrpIW765+xrZTZqsoGtvqg==", "dependencies": {"Microsoft.Extensions.Configuration": "2.1.0", "Microsoft.Extensions.DependencyInjection": "2.1.0", "Microsoft.Extensions.FileProviders.Physical": "2.1.0", "Microsoft.Extensions.Hosting.Abstractions": "2.1.0", "Microsoft.Extensions.Logging": "2.1.0"}}, "Microsoft.Extensions.Hosting.Abstractions": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "AG7HWwVRdCHlaA++1oKDxLsXIBxmDpMPb3VoyOoAghEWnkUvEAdYQUwnV4jJbAaa/nMYNiEh5ByoLauZBEiovg==", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Diagnostics.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0"}}, "Microsoft.Extensions.Http": {"type": "Transitive", "resolved": "6.0.0", "contentHash": "15+pa2G0bAMHbHewaQIdr/y6ag2H3yh4rd9hTXavtWDzQBkvpe2RMqFg8BxDpcQWssmjmBApGPcw93QRz6YcMg==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Logging": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "Microsoft.Extensions.Options": "6.0.0"}}, "Microsoft.Extensions.Logging": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "tvRkov9tAJ3xP51LCv3FJ2zINmv1P8Hi8lhhtcKGqM+ImiTCC84uOPEI4z8Cdq2C3o9e+Aa0Gw0rmrsJD77W+w==", "dependencies": {"Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}}, "Microsoft.Extensions.Logging.Abstractions": {"type": "Transitive", "resolved": "8.0.2", "contentHash": "nroMDjS7hNBPtkZqVBbSiQaQjWRDxITI8Y7XnDs97rqG3EbzVTNLZQf7bIeUJcaHOV8bca47s1Uxq94+2oGdxA==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2"}}, "Microsoft.Extensions.Logging.ApplicationInsights": {"type": "Transitive", "resolved": "2.23.0", "contentHash": "JLEabPz445i1yRB0hKZVzJJE35QatRIzWlrMOiBQXr9kBJod0jkpkrBf94ln6kXu+jlEGohnXtuXacPPhybJDw==", "dependencies": {"Microsoft.ApplicationInsights": "2.23.0", "Microsoft.Extensions.Logging": "2.1.1"}}, "Microsoft.Extensions.Logging.Configuration": {"type": "Transitive", "resolved": "2.1.0", "contentHash": "nMAcTACzW37zc3f7n5fIYsRDXtjjQA2U/kiE4xmuSLn7coCIeDfFTpUhJ+wG/3vwb5f1lFWNpyXGyQdlUCIXUw==", "dependencies": {"Microsoft.Extensions.Logging": "2.1.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "2.1.0"}}, "Microsoft.Extensions.ObjectPool": {"type": "Transitive", "resolved": "2.2.0", "contentHash": "gA8H7uQOnM5gb+L0uTNjViHYr+hRDqCdfugheGo/MxQnuHzmhhzCBTIPm19qL1z1Xe0NEMabfcOBGv9QghlZ8g=="}, "Microsoft.Extensions.Options": {"type": "Transitive", "resolved": "8.0.2", "contentHash": "dWGKvhFybsaZpGmzkGCbNNwBD1rVlWzrZKANLW/CcbFJpCEceMCGzT7zZwHOGBCbwM0SzBuceMj5HN1LKV1QqA==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Options.ConfigurationExtensions": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "0f4DMRqEd50zQh+UyJc+/HiBsZ3vhAQALgdkcQEalSH1L2isdC7Yj54M3cyo5e+BeO5fcBQ7Dxly8XiBBcvRgw==", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Primitives": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "bXJEZrW9ny8vjMF1JV253WeLhpEVzFo1lyaZu1vQ4ZxWUlVvknZ/+ftFgVheLubb4eZPSwwxBeqS1JkCOjxd8g=="}, "Microsoft.Identity.Abstractions": {"type": "Transitive", "resolved": "8.2.0", "contentHash": "q1S9TxSfa3W0w86KwNzd1hoSYaWtcZiwjHCixs4yiSyj1drcD7p5iJscd0Uxt+XsdGcrPJ6sbEZg+iYe+nb9Hw=="}, "Microsoft.Identity.Client": {"type": "Transitive", "resolved": "4.69.1", "contentHash": "CCay3mDf1czztGx92KdXNW/0sUbZC7OKh0B3OLV67YmtF09RKQHbvE3cSr0cKzAhVLv+UK3jS24ViZh0L3lWnQ==", "dependencies": {"Microsoft.IdentityModel.Abstractions": "6.35.0", "System.Diagnostics.DiagnosticSource": "6.0.1"}}, "Microsoft.Identity.Client.Extensions.Msal": {"type": "Transitive", "resolved": "4.66.1", "contentHash": "osgt1J9Rve3LO7wXqpWoFx9UFjl0oeqoUMK/xEru7dvafQ28RgV1A17CoCGCCRSUbgDQ4Arg5FgGK2lQ3lXR4A==", "dependencies": {"Microsoft.Identity.Client": "4.66.1", "System.Security.Cryptography.ProtectedData": "4.5.0"}}, "Microsoft.Identity.Web.Certificate": {"type": "Transitive", "resolved": "3.8.1", "contentHash": "B/63rzm4wYpr0XkTnM01GBL1q3AYgEMu9K/wDV+fABKmM4H8NbeXKZ++fEE/UsvPg2KuwYtQJhVh1xZxAQwajQ==", "dependencies": {"Azure.Identity": "1.11.4", "Azure.Security.KeyVault.Certificates": "4.6.0", "Azure.Security.KeyVault.Secrets": "4.6.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Identity.Abstractions": "8.2.0", "Microsoft.Identity.Web.Certificateless": "3.8.1", "Microsoft.Identity.Web.Diagnostics": "3.8.1", "System.Text.Json": "8.0.5"}}, "Microsoft.Identity.Web.Certificateless": {"type": "Transitive", "resolved": "3.8.1", "contentHash": "6zLyW6BBlNtftB5Lse/qfaqrMuU22TV4fdPt7orzg2l/9tDd8om9PRwFFBdJ9pik6uzoFdojIgMt6kcXATnKwA==", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": "2.1.0", "Microsoft.Identity.Client": "4.69.1", "Microsoft.IdentityModel.JsonWebTokens": "8.7.0", "System.Text.Json": "8.0.5"}}, "Microsoft.Identity.Web.Diagnostics": {"type": "Transitive", "resolved": "3.8.1", "contentHash": "SkG92OwxOhO0uBSUjawSZ3+nW+wUuH6bRMWMNtoESg14L+A8ejlo9SQUHzb9YfymXvDZvH0hgN7KbcK2PMhJmg=="}, "Microsoft.Identity.Web.TokenAcquisition": {"type": "Transitive", "resolved": "3.8.1", "contentHash": "iPya7GLBPzk9iN+p0P8IvjpQlUReQ1Qszrq8ZjiWHlVrghIjTSFltLjVfvsOpcHRNhDqD91TkwecYpCqZGAmQg==", "dependencies": {"Microsoft.AspNetCore.Authentication.JwtBearer": "8.0.0", "Microsoft.AspNetCore.Authentication.OpenIdConnect": "8.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0", "Microsoft.Identity.Abstractions": "8.2.0", "Microsoft.Identity.Web.Certificate": "3.8.1", "Microsoft.Identity.Web.Certificateless": "3.8.1", "Microsoft.Identity.Web.TokenCache": "3.8.1", "Microsoft.IdentityModel.Logging": "8.7.0", "Microsoft.IdentityModel.LoggingExtensions": "8.7.0", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "8.7.0", "System.IdentityModel.Tokens.Jwt": "8.7.0"}}, "Microsoft.Identity.Web.TokenCache": {"type": "Transitive", "resolved": "3.8.1", "contentHash": "pAumqwmOUbsqA/uEW1cSv/8mj9nvtgpEudn5IzQ5xDeeN6KJ+jvhp7bmTOG76HNBXZpa+tjBt24P4S60hC7oKA==", "dependencies": {"Microsoft.AspNetCore.DataProtection": "8.0.1", "Microsoft.Extensions.Caching.Memory": "8.0.1", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Identity.Client": "4.69.1", "Microsoft.Identity.Web.Diagnostics": "3.8.1", "System.Formats.Asn1": "8.0.1", "System.Security.Cryptography.Pkcs": "8.0.0", "System.Security.Cryptography.Xml": "8.0.1"}}, "Microsoft.IdentityModel.Abstractions": {"type": "Transitive", "resolved": "8.7.0", "contentHash": "OQd5aVepYvh5evOmBMeAYjMIpEcTf1ZCBZaU7Nh/RlhhdXefjFDJeP1L2F2zeNT1unFr+wUu/h3Ac2Xb4BXU6w=="}, "Microsoft.IdentityModel.Logging": {"type": "Transitive", "resolved": "8.7.0", "contentHash": "Bs0TznPAu+nxa9rAVHJ+j3CYECHJkT3tG8AyBfhFYlT5ldsDhoxFT7J+PKxJHLf+ayqWfvDZHHc4639W2FQCxA==", "dependencies": {"Microsoft.IdentityModel.Abstractions": "8.7.0"}}, "Microsoft.IdentityModel.LoggingExtensions": {"type": "Transitive", "resolved": "8.7.0", "contentHash": "sKiaukUl098FlPaD91p65Go6AvK2CzFPZvA8xtvcujb+9k801dFeKtkr5f4o/WSSCv00FTwwTc8Lo5Cl/5kRaw==", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": "2.1.1", "Microsoft.IdentityModel.Abstractions": "8.7.0"}}, "Microsoft.IdentityModel.Protocols": {"type": "Transitive", "resolved": "8.7.0", "contentHash": "4r4H8LCCoNFlJHRrWCqaNtzJPM4Bfi9ARdl7Gd+OoIZqc1rsp9z60USIf00o5YwAwXwKffPUPrvufkbgR69jYA==", "dependencies": {"Microsoft.IdentityModel.Tokens": "8.7.0"}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect": {"type": "Transitive", "resolved": "8.7.0", "contentHash": "nUifCAs2E9cvBiYBC3/L9PoftSxTVpdUdoIu7VV9M9aw7mogsdFRUn5v23c5Jl9u93jdUc0PCagrItLHncG8Qg==", "dependencies": {"Microsoft.IdentityModel.Protocols": "8.7.0", "System.IdentityModel.Tokens.Jwt": "8.7.0"}}, "Microsoft.IdentityModel.Tokens": {"type": "Transitive", "resolved": "8.7.0", "contentHash": "5Z6voXjRXAnGklhmZd1mKz89UhcF5ZQQZaZc2iKrOuL4Li1UihG2vlJx8IbiFAOIxy/xdbsAm0A+WZEaH5fxng==", "dependencies": {"Microsoft.Bcl.Memory": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.IdentityModel.Logging": "8.7.0"}}, "Microsoft.IdentityModel.Validators": {"type": "Transitive", "resolved": "8.7.0", "contentHash": "3jZQ1y8H6Gm9JtwcPKqtmN2CTVyHNiAQIO+CeGBl/NJgoQ5v55X2OLjzZmAT3QObA8E5gewjHaQRapraWph2BQ==", "dependencies": {"Microsoft.IdentityModel.Protocols": "8.7.0", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "8.7.0", "Microsoft.IdentityModel.Tokens": "8.7.0", "System.IdentityModel.Tokens.Jwt": "8.7.0"}}, "Microsoft.Net.Http.Headers": {"type": "Transitive", "resolved": "2.2.0", "contentHash": "iZNkjYqlo8sIOI0bQfpsSoMTmB/kyvmV2h225ihyZT33aTp48ZpF6qYnXxzSXmHt8DpBAwBTX+1s1UFLbYfZKg==", "dependencies": {"Microsoft.Extensions.Primitives": "2.2.0", "System.Buffers": "4.5.0"}}, "Microsoft.NETCore.Platforms": {"type": "Transitive", "resolved": "2.0.0", "contentHash": "VdLJOCXhZaEMY7Hm2GKiULmn7IEPFE4XC5LPSfBVCUIA8YLZVh846gtfBJalsPQF2PlzdD7ecX7DZEulJ402ZQ=="}, "Microsoft.NETCore.Targets": {"type": "Transitive", "resolved": "1.1.0", "contentHash": "aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg=="}, "Microsoft.OpenApi": {"type": "Transitive", "resolved": "1.6.23", "contentHash": "tZ1I0KXnn98CWuV8cpI247A17jaY+ILS9vvF7yhI0uPPEqF4P1d7BWL5Uwtel10w9NucllHB3nTkfYTAcHAh8g=="}, "Microsoft.Win32.SystemEvents": {"type": "Transitive", "resolved": "6.0.0", "contentHash": "hqTM5628jSsQiv+HGpiq3WKBl2c8v1KZfby2J6Pr7pEPlK9waPdgEO6b8A/+/xn/yZ9ulv8HuqK71ONy2tg67A=="}, "Newtonsoft.Json": {"type": "Transitive", "resolved": "13.0.3", "contentHash": "HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ=="}, "Newtonsoft.Json.Bson": {"type": "Transitive", "resolved": "1.0.2", "contentHash": "QYFyxhaABwmq3p/21VrZNYvCg3DaEoN/wUuw5nmfAf0X3HLjgupwhkEWdgfb9nvGAUIv3osmZoD3kKl4jxEmYQ==", "dependencies": {"Newtonsoft.Json": "12.0.1"}}, "OpenAI": {"type": "Transitive", "resolved": "2.1.0", "contentHash": "zl72nyP4O94ZyEoCLoE8qoc5vp8kSJmRuqR+UdK+jNIjFJGWJYjsb4rAGpMWWRTbPYuk82E8myMJdVQurMLDnQ==", "dependencies": {"System.ClientModel": "1.2.1", "System.Diagnostics.DiagnosticSource": "6.0.1"}}, "Polly": {"type": "Transitive", "resolved": "7.2.3", "contentHash": "DeCY0OFbNdNxsjntr1gTXHJ5pKUwYzp04Er2LLeN3g6pWhffsGuKVfMBLe1lw7x76HrPkLxKEFxBlpRxS2nDEQ=="}, "Polly.Extensions.Http": {"type": "Transitive", "resolved": "3.0.0", "contentHash": "drrG+hB3pYFY7w1c3BD+lSGYvH2oIclH8GRSehgfyP5kjnFnHKQuuBhuHLv+PWyFuaTDyk/vfRpnxOzd11+J8g==", "dependencies": {"Polly": "7.1.0"}}, "runtime.native.System": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "c/qWt2LieNZIj1jGnVNsE2Kl23Ya2aSTBuXMD6V7k9KWr6l16Tqdwq+hJScEpWER9753NWC8h96PaVNY5Ld7Jw==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "Swashbuckle.AspNetCore.Swagger": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "+8Y4pVTWbnzotIk6d6rcwsHGpCchPDqqrvYkyGlI3go+pFaKM+4eX30iCyI0hvr0RMtObJCFhK6aDtlQFbEF1g==", "dependencies": {"Microsoft.OpenApi": "1.6.23"}}, "Swashbuckle.AspNetCore.SwaggerGen": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "skCeIQ93yMcUm1PQby5qitFM6KLIlLMj4/i8JHy86x2OFzxTNaaas2kUg6rNV3JvucFvYCNyImg7NMtZHErSzQ==", "dependencies": {"Swashbuckle.AspNetCore.Swagger": "8.0.0"}}, "Swashbuckle.AspNetCore.SwaggerUI": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "IMqmgclFiZL2QIfopOmWYofZzckrl+SdMt1h4mKC0jc94F+uzt3IHA3YFC0CGlwBqTTSnxHqNUKomNTeAhZbYA=="}, "System.AppContext": {"type": "Transitive", "resolved": "4.1.0", "contentHash": "3QjO4jNV7PdKkmQAVp9atA+usVnKRwI3Kx1nMwJ93T0LcQfx7pKAYk0nKz5wn1oP5iqlhZuy6RXOFdhr7rDwow==", "dependencies": {"System.Runtime": "4.1.0"}}, "System.Buffers": {"type": "Transitive", "resolved": "4.5.0", "contentHash": "pL2ChpaRRWI/p4LXyy4RgeWlYF2sgfj/pnVMvBqwNFr5cXg7CXNnWZWxrOONLg8VGdFB8oB+EG2Qw4MLgTOe+A=="}, "System.ClientModel": {"type": "Transitive", "resolved": "1.2.1", "contentHash": "s9+M5El+DXdCRRLzxak8uGBKWT8H/eIssGpFtpaMKdJULrQbBDPH/zFbVyHX+NDczhS5EvjHFbBH9/L+0UhmcA==", "dependencies": {"System.Memory.Data": "6.0.0", "System.Text.Json": "6.0.10"}}, "System.Collections": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Collections.Immutable": {"type": "Transitive", "resolved": "6.0.0", "contentHash": "l4zZJ1WU2hqpQQHXz1rvC3etVZN+2DLmQMO79FhOTZHMn8tDRr+WU287sbomD0BETlmKDn0ygUgVy9k5xkkJdA==", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.ComponentModel.Annotations": {"type": "Transitive", "resolved": "5.0.0", "contentHash": "dMkqfy2el8A8/I76n2Hi1oBFEbG1SfxD2l5nhwXV3XjlnOmwxJlQbYpJH4W51odnU9sARCSAgv7S3CyAFMkpYg=="}, "System.Configuration.ConfigurationManager": {"type": "Transitive", "resolved": "6.0.0", "contentHash": "7T+m0kDSlIPTHIkPMIu6m6tV6qsMqJpvQWW2jIc2qi7sn40qxFo0q+7mEQAhMPXZHMKnWrnv47ntGlM/ejvw3g==", "dependencies": {"System.Security.Cryptography.ProtectedData": "6.0.0", "System.Security.Permissions": "6.0.0"}}, "System.Diagnostics.Debug": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Diagnostics.DiagnosticSource": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "c9xLpVz6PL9lp/djOWtk5KPDZq3cSYpmXoJQY524EOtuFl5z9ZtsotpsyrDW40U1DRnQSYvcPKEUV0X//u6gkQ=="}, "System.Diagnostics.PerformanceCounter": {"type": "Transitive", "resolved": "6.0.0", "contentHash": "gbeE5tNp/oB7O8kTTLh3wPPJCxpNOphXPTWVs1BsYuFOYapFijWuh0LYw1qnDo4gwDUYPXOmpTIhvtxisGsYOQ==", "dependencies": {"System.Configuration.ConfigurationManager": "6.0.0"}}, "System.Diagnostics.TraceSource": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "VnYp1NxGx8Ww731y2LJ1vpfb/DKVNKEZ8Jsh5SgQTZREL/YpWRArgh9pI8CDLmgHspZmLL697CaLvH85qQpRiw==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0"}}, "System.Drawing.Common": {"type": "Transitive", "resolved": "6.0.0", "contentHash": "NfuoKUiP2nUWwKZN6twGqXioIe1zVD0RIj2t976A+czLHr2nY454RwwXs6JU9Htc6mwqL6Dn/nEL3dpVf2jOhg==", "dependencies": {"Microsoft.Win32.SystemEvents": "6.0.0"}}, "System.Dynamic.Runtime": {"type": "Transitive", "resolved": "4.0.11", "contentHash": "db34f6LHYM0U0JpE+sOmjar27BnqTVkbLJhgfwMpTdgTigG/Hna3m2MYVwnFzGGKnEJk2UXFuoVTr8WUbU91/A==", "dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Globalization": "4.0.11", "System.Linq": "4.1.0", "System.Linq.Expressions": "4.1.0", "System.ObjectModel": "4.0.12", "System.Reflection": "4.1.0", "System.Reflection.Emit": "4.0.1", "System.Reflection.Emit.ILGeneration": "4.0.1", "System.Reflection.Primitives": "4.0.1", "System.Reflection.TypeExtensions": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11"}}, "System.Formats.Asn1": {"type": "Transitive", "resolved": "8.0.1", "contentHash": "XqKba7Mm/koKSjKMfW82olQdmfbI5yqeoLV/tidRp7fbh5rmHAQ5raDI/7SU0swTzv+jgqtUGkzmFxuUg0it1A=="}, "System.Globalization": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.IdentityModel.Tokens.Jwt": {"type": "Transitive", "resolved": "8.7.0", "contentHash": "8dKL3A9pVqYCJIXHd4H2epQqLxSvKeNxGonR0e5g89yMchyvsM/NLuB06otx29BicUd6+LUJZgNZmvYjjPsPGg==", "dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "8.7.0", "Microsoft.IdentityModel.Tokens": "8.7.0"}}, "System.IO": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.FileSystem": {"type": "Transitive", "resolved": "4.0.1", "contentHash": "IBErlVq5jOggAD69bg1t0pJcHaDbJbWNUZTPI96fkYWzwYbN6D9wRHMULLDd9dHsl7C2YsxXL31LMfPI1SWt8w==", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.IO": "4.1.0", "System.IO.FileSystem.Primitives": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Handles": "4.0.1", "System.Text.Encoding": "4.0.11", "System.Threading.Tasks": "4.0.11"}}, "System.IO.FileSystem.AccessControl": {"type": "Transitive", "resolved": "4.7.0", "contentHash": "vMToiarpU81LR1/KZtnT7VDPvqAZfw9oOS5nY6pPP78nGYz3COLsQH3OfzbR+SjTgltd31R6KmKklz/zDpTmzw==", "dependencies": {"System.Security.AccessControl": "4.7.0", "System.Security.Principal.Windows": "4.7.0"}}, "System.IO.FileSystem.Primitives": {"type": "Transitive", "resolved": "4.0.1", "contentHash": "kWkKD203JJKxJeE74p8aF8y4Qc9r9WQx4C0cHzHPrY3fv/L/IhWnyCHaFJ3H1QPOH6A93whlQ2vG5nHlBDvzWQ==", "dependencies": {"System.Runtime": "4.1.0"}}, "System.IO.Hashing": {"type": "Transitive", "resolved": "6.0.0", "contentHash": "Rfm2jYCaUeGysFEZjDe7j1R4x6Z6BzumS/vUT5a1AA/AWJuGX71PoGB0RmpyX3VmrGqVnAwtfMn39OHR8Y/5+g=="}, "System.IO.Pipelines": {"type": "Transitive", "resolved": "9.0.3", "contentHash": "aP1Qh9llcEmo0qN+VKvVDHFMe5Cqpfb1VjhBO7rjmxCXtLs3IfVSOiNqqLBZ/4Qbcr4J0SDdJq9S7EKAGpnwEA=="}, "System.Linq": {"type": "Transitive", "resolved": "4.1.0", "contentHash": "bQ0iYFOQI0nuTnt+NQADns6ucV4DUvMdwN6CbkB1yj8i7arTGiTN5eok1kQwdnnNWSDZfIUySQY+J3d5KjWn0g==", "dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0"}}, "System.Linq.Expressions": {"type": "Transitive", "resolved": "4.1.0", "contentHash": "I+y02iqkgmCAyfbqOmSDOgqdZQ5tTj80Akm5BPSS8EeB0VGWdy6X1KCoYe8Pk6pwDoAKZUOdLVxnTJcExiv5zw==", "dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Globalization": "4.0.11", "System.IO": "4.1.0", "System.Linq": "4.1.0", "System.ObjectModel": "4.0.12", "System.Reflection": "4.1.0", "System.Reflection.Emit": "4.0.1", "System.Reflection.Emit.ILGeneration": "4.0.1", "System.Reflection.Emit.Lightweight": "4.0.1", "System.Reflection.Extensions": "4.0.1", "System.Reflection.Primitives": "4.0.1", "System.Reflection.TypeExtensions": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11"}}, "System.Memory": {"type": "Transitive", "resolved": "4.5.5", "contentHash": "XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw=="}, "System.Memory.Data": {"type": "Transitive", "resolved": "6.0.1", "contentHash": "yliDgLh9S9Mcy5hBIdZmX6yphYIW3NH+3HN1kV1m7V1e0s7LNTw/tHNjJP4U9nSMEgl3w1TzYv/KA1Tg9NYy6w=="}, "System.ObjectModel": {"type": "Transitive", "resolved": "4.0.12", "contentHash": "tAgJM1xt3ytyMoW4qn4wIqgJYm7L7TShRZG4+Q4Qsi2PCcj96pXN7nRywS9KkB3p/xDUjc2HSwP9SROyPYDYKQ==", "dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Threading": "4.0.11"}}, "System.Reflection": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit": {"type": "Transitive", "resolved": "4.0.1", "contentHash": "P2wqAj72fFjpP6wb9nSfDqNBMab+2ovzSDzUZK7MVIm54tBJEPr9jWfSjjoTpPwj1LeKcmX3vr0ttyjSSFM47g==", "dependencies": {"System.IO": "4.1.0", "System.Reflection": "4.1.0", "System.Reflection.Emit.ILGeneration": "4.0.1", "System.Reflection.Primitives": "4.0.1", "System.Runtime": "4.1.0"}}, "System.Reflection.Emit.ILGeneration": {"type": "Transitive", "resolved": "4.0.1", "contentHash": "Ov6dU8Bu15Bc7zuqttgHF12J5lwSWyTf1S+FJouUXVMSqImLZzYaQ+vRr1rQ0OZ0HqsrwWl4dsKHELckQkVpgA==", "dependencies": {"System.Reflection": "4.1.0", "System.Reflection.Primitives": "4.0.1", "System.Runtime": "4.1.0"}}, "System.Reflection.Emit.Lightweight": {"type": "Transitive", "resolved": "4.0.1", "contentHash": "sSzHHXueZ5Uh0OLpUQprhr+ZYJrLPA2Cmr4gn0wj9+FftNKXx8RIMKvO9qnjk2ebPYUjZ+F2ulGdPOsvj+MEjA==", "dependencies": {"System.Reflection": "4.1.0", "System.Reflection.Emit.ILGeneration": "4.0.1", "System.Reflection.Primitives": "4.0.1", "System.Runtime": "4.1.0"}}, "System.Reflection.Extensions": {"type": "Transitive", "resolved": "4.0.1", "contentHash": "GYrtRsZcMuHF3sbmRHfMYpvxZoIN2bQGrYGerUiWLEkqdEUQZhH3TRSaC/oI4wO0II1RKBPlpIa1TOMxIcOOzQ==", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Reflection": "4.1.0", "System.Runtime": "4.1.0"}}, "System.Reflection.Metadata": {"type": "Transitive", "resolved": "1.6.0", "contentHash": "COC1aiAJjCoA5GBF+QKL2uLqEBew4JsCkQmoHKbN3TlOZKa2fKLz5CpiRQKDz0RsAOEGsVKqOD5bomsXq/4STQ=="}, "System.Reflection.Primitives": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Reflection.TypeExtensions": {"type": "Transitive", "resolved": "4.1.0", "contentHash": "tsQ/ptQ3H5FYfON8lL4MxRk/8kFyE0A+tGPXmVP967cT/gzLHYxIejIYSxp4JmIeFHVP78g/F2FE1mUUTbDtrg==", "dependencies": {"System.Reflection": "4.1.0", "System.Runtime": "4.1.0"}}, "System.Resources.ResourceManager": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Runtime": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "System.Runtime.CompilerServices.Unsafe": {"type": "Transitive", "resolved": "6.0.0", "contentHash": "/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg=="}, "System.Runtime.Extensions": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime.Handles": {"type": "Transitive", "resolved": "4.0.1", "contentHash": "nCJvEKguXEvk2ymk1gqj625vVnlK3/xdGzx0vOKicQkoquaTBJTP13AIYkocSUwHCLNBwUbXTqTWGDxBTWpt7g==", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0"}}, "System.Runtime.InteropServices": {"type": "Transitive", "resolved": "4.1.0", "contentHash": "16eu3kjHS633yYdkjwShDHZLRNMKVi/s0bY8ODiqJ2RfMhDMAwxZaUaWVnZ2P71kr/or+X9o/xFWtNqz8ivieQ==", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Reflection": "4.1.0", "System.Reflection.Primitives": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Handles": "4.0.1"}}, "System.Runtime.InteropServices.RuntimeInformation": {"type": "Transitive", "resolved": "4.0.0", "contentHash": "hWPhJxc453RCa8Z29O91EmfGeZIHX1ZH2A8L6lYQVSaKzku2DfArSfMEb1/MYYzPQRJZeu0c9dmYeJKxW5Fgng==", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "System.Reflection": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.InteropServices": "4.1.0", "System.Threading": "4.0.11", "runtime.native.System": "4.0.0"}}, "System.Security.AccessControl": {"type": "Transitive", "resolved": "6.0.0", "contentHash": "AUADIc0LIEQe7MzC+I0cl0rAT8RrTAKFHl53yHjEUzNVIaUlhFY11vc2ebiVJzVBuOzun6F7FBA+8KAbGTTedQ=="}, "System.Security.Cryptography.Pkcs": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "ULmp3xoOwNYjOYp4JZ2NK/6NdTgiN1GQXzVVN1njQ7LOZ0d0B9vyMnhyqbIi9Qw4JXj1JgCsitkTShboHRx7Eg==", "dependencies": {"System.Formats.Asn1": "8.0.0"}}, "System.Security.Cryptography.ProtectedData": {"type": "Transitive", "resolved": "6.0.0", "contentHash": "rp1gMNEZpvx9vP0JW0oHLxlf8oSiQgtno77Y4PLUBjSiDYoD77Y8uXHr1Ea5XG4/pIKhqAdxZ8v8OTUtqo9PeQ=="}, "System.Security.Cryptography.Xml": {"type": "Transitive", "resolved": "8.0.1", "contentHash": "hqu2ztecOf3BYg5q1R7QcyliX9L7r3mfsWtaRitAxcezH8hyZMB7zCmhi186hsUZXk1KxsAHXwyPEW+xvUED6g==", "dependencies": {"System.Security.Cryptography.Pkcs": "8.0.0"}}, "System.Security.Permissions": {"type": "Transitive", "resolved": "6.0.0", "contentHash": "T/uuc7AklkDoxmcJ7LGkyX1CcSviZuLCa4jg3PekfJ7SU0niF0IVTXwUiNVP9DSpzou2PpxJ+eNY2IfDM90ZCg==", "dependencies": {"System.Security.AccessControl": "6.0.0", "System.Windows.Extensions": "6.0.0"}}, "System.Security.Principal.Windows": {"type": "Transitive", "resolved": "4.7.0", "contentHash": "ojD0PX0XhneCsUbAZVKdb7h/70vyYMDYs85lwEI+LngEONe/17A0cFaRFqZU+sOEidcVswYWikYOQ9PPfjlbtQ=="}, "System.Text.Encoding": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Text.Encoding.CodePages": {"type": "Transitive", "resolved": "4.5.0", "contentHash": "S0wEUiKcLvRlkFUXca8uio1UQ5bYQzYgOmOKtCqaBQC3GR9AJjh43otcM32IGsAyvadFTaAMw9Irm6dS4Evfng==", "dependencies": {"Microsoft.NETCore.Platforms": "2.0.0", "System.Runtime.CompilerServices.Unsafe": "4.5.0"}}, "System.Text.Encodings.Web": {"type": "Transitive", "resolved": "9.0.3", "contentHash": "5L+iI4fBMtGwt4FHLQh40/rgdbxnw6lHaLkR3gbaHG97TohzUv+z/oP03drsTR1lKCLhOkp40cFnHYOQLtpT5A=="}, "System.Text.Json": {"type": "Transitive", "resolved": "9.0.3", "contentHash": "r2JRkLjsYrq5Dpo7+y3Wa73OfirZPdVhxiTJWwZ+oJM7FOAe0LkM3GlH+pgkNRdd1G1kwUbmRCdmh4uoaWwu1g==", "dependencies": {"System.IO.Pipelines": "9.0.3", "System.Text.Encodings.Web": "9.0.3"}}, "System.Threading": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "dependencies": {"System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.Channels": {"type": "Transitive", "resolved": "6.0.0", "contentHash": "TY8/9+tI0mNaUMgntOxxaq2ndTkdXqLSxvPmas7XEqOlv9lQtB7wLjYGd756lOaO7Dvb5r/WXhluM+0Xe87v5Q=="}, "System.Threading.Tasks": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Threading.Tasks.Dataflow": {"type": "Transitive", "resolved": "4.8.0", "contentHash": "PSIdcgbyNv7FZvZ1I9Mqy6XZOwstYYMdZiXuHvIyc0gDyPjEhrrP9OvTGDHp+LAHp1RNSLjPYssyqox9+Kt9Ug=="}, "System.Threading.Tasks.Extensions": {"type": "Transitive", "resolved": "4.5.4", "contentHash": "zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg=="}, "System.Windows.Extensions": {"type": "Transitive", "resolved": "6.0.0", "contentHash": "IXoJOXIqc39AIe+CIR7koBtRGMiCt/LPM3lI+PELtDIy9XdyeSrwXFdWV9dzJ2Awl0paLWUaknLxFQ5HpHZUog==", "dependencies": {"System.Drawing.Common": "6.0.0"}}, "YamlDotNet": {"type": "Transitive", "resolved": "13.1.0", "contentHash": "S4tzJjofMDx8PKtbnwkaMznbPD1aUffVKW+ClhfOGAYFIPuY9sSrArcFQOcRkbwRP1kYIaMbXk4Vsnfk3dE3dQ=="}, "Zipangu": {"type": "Transitive", "resolved": "1.1.8", "contentHash": "msGAfKpGJ0ZPgkRSIMwqRfx0ktDAEq4dDsMfVoA5ca1hVWQr0iTOBOi3LRosNSJ8/fJY5dbd4WYumm8LuooR6g==", "dependencies": {"System.Text.Encoding.CodePages": "4.5.0"}}, "avanade.geranium.attane.business": {"type": "Project", "dependencies": {"Avanade.Geranium.Attane.Infrastructure": "[1.0.0, )", "Avanade.Geranium.Attane.Shared": "[1.0.0, )", "Azure.AI.OpenAI": "[2.1.0, )", "Azure.Identity": "[1.13.1, )", "Azure.Search.Documents": "[11.7.0-beta.1, )", "CoreEx": "[2.10.1, )", "CoreEx.Validation": "[2.10.1, )", "Microsoft.Extensions.Logging.Abstractions": "[8.0.0, )", "Zipangu": "[1.1.8, )"}}, "avanade.geranium.attane.common": {"type": "Project", "dependencies": {"Avanade.Geranium.Attane.Shared": "[1.0.0, )", "CoreEx": "[2.10.1, )"}}, "avanade.geranium.attane.infrastructure": {"type": "Project", "dependencies": {"Azure.Core": "[1.45.0, )", "Azure.Data.Tables": "[12.10.0, )", "Azure.Storage.Queues": "[12.22.0, )", "CoreEx": "[2.10.1, )", "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions": "[9.0.3, )"}}, "avanade.geranium.attane.shared": {"type": "Project", "dependencies": {"System.Text.Json": "[9.0.3, )"}}}}}