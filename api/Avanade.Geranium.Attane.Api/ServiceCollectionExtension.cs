// Beef生成機能のうち使用しないものをコメントアウトしているため
#pragma warning disable IDE0079 // 不要な抑制を削除します
#pragma warning disable S125 // Sections of code should not be commented out
#pragma warning restore IDE0079 // 不要な抑制を削除します
using Avanade.Geranium.Attane.Api.Caller;
using Avanade.Geranium.Attane.Api.Telemetry;
using Avanade.Geranium.Attane.Api.Token;
using Avanade.Geranium.Attane.Business.Configuration;
using Avanade.Geranium.Attane.Infrastructure.Data.Clients;

using CoreEx.Azure.HealthChecks;
using CoreEx.HealthChecks;
using Microsoft.ApplicationInsights.AspNetCore.Extensions;
using Microsoft.ApplicationInsights.Extensibility;
using Azure.Data.Tables;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Logging.ApplicationInsights;

namespace Avanade.Geranium.Attane.Api;

/// <summary>
/// <see cref="IServiceCollection"/> に対する拡張メソッドを定義します。
/// </summary>
public static class ServiceCollectionExtension
{
    /// <summary>
    /// Beefで必要となる処理を行います。
    /// </summary>
    /// <param name="services">対象の<see cref="IServiceCollection"/></param>
    /// <param name="config"><see cref="GeraniumAttaneSettings"/></param>
    /// <returns>チェーン可能とするために、与えられた <see cref="IServiceCollection"/> を返します。</returns>
    public static IServiceCollection AddBeef(this IServiceCollection services, GeraniumAttaneSettings config)
    {
        // Add the core beef services.
        services
            .AddExecutionContext()
            .AddJsonSerializer()
#if RefData
            .AddReferenceDataOrchestrator()
#endif
            .AddWebApi()
            .AddJsonMergePatch()
#if RefData
            .AddReferenceDataContentWebApi()
#endif
            .AddRequestCache()
            .AddValidationTextProvider()
            .AddValidators<GeraniumAttaneSettings>()
            .AddSingleton<IIdentifierGenerator, IdentifierGenerator>();

#if RefData
        // Add the generated reference data services.
        services
            .AddGeneratedReferenceDataManagerServices()
            .AddGeneratedReferenceDataDataSvcServices()
            .AddGeneratedReferenceDataDataServices();
#endif

        // Add the generated entity services.
        services
            .AddGeneratedManagerServices()
            .AddGeneratedDataSvcServices()
            .AddGeneratedDataServices();

        services
            .AddEventDataSerializer()
            .AddEventDataFormatter();

        // Add additional services.
        services.AddControllers();
        services.AddScoped<HealthService>();
        var healthChecksBuilder = services.AddHealthChecks();
        // Add event publishing services.

        if (config.ServiceBusConnection != null)
        {
            var queueName = config.QueueName;
            services
                .AddAzureServiceBusSender(
                    configure: (_, s) =>
                    {
                        s.DefaultQueueOrTopicName = queueName;
                    }
                )
                .AddEventPublisher()
                .AddAzureServiceBusPurger()
                .AddAzureServiceBusClient(
                    connectionName: nameof(GeraniumAttaneSettings.ServiceBusConnection),
                    configure: (o, _) =>
                    {
                        o.RetryOptions.MaxRetries = 3;
                    });
            healthChecksBuilder.AddTypeActivatedCheck<AzureServiceBusQueueHealthCheck>(
                "Health check for service bus verification queue",
                HealthStatus.Unhealthy,
                nameof(GeraniumAttaneSettings.ServiceBusConnection),
                nameof(GeraniumAttaneSettings.QueueName));
        }
        else
        {
            services
                .AddNullEventPublisher();
            throw new ArgumentNullException(nameof(GeraniumAttaneSettings.ServiceBusConnection));
        }

        services.AddJsonSerializer();
        services.AddStorageAccounts(config, healthChecksBuilder);
        services.AddHttpClient();

        return services;
    }

    /// <summary>
    /// Swaggerで必要となる処理を行います。
    /// </summary>
    /// <param name="services">対象の<see cref="IServiceCollection"/></param>
    /// <returns>チェーン可能とするために、与えられた <see cref="IServiceCollection"/> を返します。</returns>
    public static IServiceCollection AddSwagger(this IServiceCollection services)
    {
        services.AddSwaggerGen(c =>
        {
            c.SwaggerDoc("v1", new OpenApiInfo { Title = "Avanade.Geranium.Attane API", Version = "v1" });

            // Webコンソール上に認証を追加
            c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
            {
                Description = "JWT Authorization header using the Bearer scheme",
                Name = "Authorization",
                In = ParameterLocation.Header,
                Type = SecuritySchemeType.ApiKey,
            });
            c.AddSecurityRequirement(new OpenApiSecurityRequirement
            {
                {
                    new OpenApiSecurityScheme
                    {
                        Name = "Bearer",
                        Type = SecuritySchemeType.ApiKey,
                        In = ParameterLocation.Header,
                        Reference = new OpenApiReference
                        {
                            Type = ReferenceType.SecurityScheme,
                            Id = "Bearer",
                        },
                    }, Array.Empty<string>()
                }
            });

            // ビルド時に生成されるXMLコメントを用いる
            var xmlName = $"{Assembly.GetEntryAssembly()!.GetName().Name}.xml";
            var xmlFile = Path.Combine(AppContext.BaseDirectory, xmlName);
            if (File.Exists(xmlFile))
                c.IncludeXmlComments(xmlFile);
        });

        return services;
    }

    /// <summary>
    /// Application Insightsの構成を行います。
    /// </summary>
    /// <param name="services">The <see cref="IServiceCollection"/>.</param>
    /// <param name="config">The <see cref="GeraniumAttaneSettings"/>.</param>
    public static IServiceCollection AddApplicationInsights(this IServiceCollection services, GeraniumAttaneSettings config)
    {
        if (config.AppInsightsConnection != null)
        {
            var options = new ApplicationInsightsServiceOptions()
            {
                EnableAdaptiveSampling = false,
                EnableDebugLogger = false,
            };
            services.AddApplicationInsightsTelemetry(options);
            services.AddSingleton<ITelemetryInitializer, GeraniumTelemetryInitializer>();

            RemoveDefaultFilterForApplicationInsightsLogger(services);
        }

        return services;
    }

    /// <summary>
    /// Geranium固有のDI構成を行います。
    /// </summary>
    /// <param name="services">The <see cref="IServiceCollection"/>.</param>
    /// <param name="config">The <see cref="IConfiguration"/>.</param>
    public static IServiceCollection AddGeraniumDependencies(this IServiceCollection services, IConfiguration config)
    {
        services.AddSingleton<ICallerProvider, BearerCallerProvider>();
        services.AddSingleton<ITokenIssuer, OboTokenIssuer>();

        services.Configure<SearchConfiguration>(config.GetSection("Geranium:Search"));

        return services;
    }

    /// <summary>
    /// Storage AccountおよびTable ClientのDI構成を行います。
    /// </summary>
    /// <param name="services">The <see cref="IServiceCollection"/>.</param>
    /// <param name="config">The <see cref="GeraniumAttaneSettings"/>.</param>
    /// <param name="healthChecksBuilder">The <see cref="IHealthChecksBuilder"/></param>
    public static IServiceCollection AddStorageAccounts(this IServiceCollection services, GeraniumAttaneSettings config, IHealthChecksBuilder healthChecksBuilder)
    {
        var connectionString = config.StorageAccountConnection;
        if (connectionString == null)
        {
            return services;
        }

        var serviceClient = new TableServiceClient(connectionString);
        services.AddSingleton(serviceClient);

        services.AddScoped<ITableStorageClient, TableStorageClient>();

        healthChecksBuilder.AddAsyncCheck("Table Storage", () => TableStorageClient.HealthCheck(serviceClient,
            "search",
            "bookmarks",
            "users",
            "teamschats"
        ));

        var queueClient = new QueueStorageClient(connectionString);
        services.AddSingleton<IQueueStorageClient>(queueClient);

        healthChecksBuilder.AddAsyncCheck("Queue Storage", () => queueClient.HealthCheck(
            "search-process"
        ));

        return services;
    }

    /// <summary>
    /// Application InsightsへのLoggerにつけられている既定のフィルタを削除します。
    /// </summary>
    /// <param name="services">サービス</param>
    /// <remarks>
    /// 既定ではWarning以上のみログ出力するよう構成されているため
    /// </remarks>
    public static void RemoveDefaultFilterForApplicationInsightsLogger(this IServiceCollection services)
    {
        services.PostConfigureAll<LoggerFilterOptions>(action =>
        {
            var matchingRule = action.Rules.FirstOrDefault(
                x => x.ProviderName == typeof(ApplicationInsightsLoggerProvider).FullName);
            if (matchingRule != null)
            {
                action.Rules.Remove(matchingRule);
            }
        });
    }
}


#pragma warning disable IDE0079 // 不要な抑制を削除します
#pragma warning restore S125 // Sections of code should not be commented out
#pragma warning restore IDE0079 // 不要な抑制を削除します
